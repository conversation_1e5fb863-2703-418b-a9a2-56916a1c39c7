template = """
data=<data>;
databf=ts_backfill(data, <bf_days>);
signal=<sign>group_neutralize(databf, <group>);
ts_signal=<ts_op>(signal, <op_days>);
<op>(ts_signal)
"""
parameter_space = {
    "<sign>": ["", "-"],
    "<bf_days>": ["21", "63", "252"],
    "<op_days>": ["1", "21", "63", "252"],
    "<group>": ["sector", "industry", "subindustry"],
    "<op>": ["rank", "zscore", "windsorize"],
    "<ts_op>": ["ts_delta", "ts_zscore", "ts_mean"],
    "<data>": [
        "mdl77_2rel5yfwdep",
        "mdl77_2garpanalystmodel_qgp_vfpriceratio",
        "mdl77_2400_yen",
        "mdl77_2garpanalystmodel_qgp_capeff",
        "mdl77_2deepvaluefactor_curep",
        "mdl77_2400_chg12msip",
    ],
}

settings_space={
    'region': ['USA'],
    'universe': ['TOP3000'],
    'decay': [5, 10, 15],
    'neutralization': ['SUBINDUSTRY'],
    'truncation': [0.08],
}
