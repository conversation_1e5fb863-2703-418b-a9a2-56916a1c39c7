template = """
data=<data>;
databf=ts_backfill(data, <bf_days>);
signal=<sign>group_neutralize(databf, <group>);
ts_signal=<ts_op>(signal, <op_days>);
<op>(ts_signal)
"""
parameter_space = {
    "<sign>": ["", "-"],
    "<bf_days>": ["21"],
    "<op_days>": ["1", "21"],
    "<group>": ["sector", "industry", "subindustry"],
    "<op>": ["rank", "zscore", "windsorize"],
    "<ts_op>": ["ts_delta", "ts_zscore"],
    "<data>": [
        "mdl77_2rel5yfwdep",
        "mdl77_2garpanalystmodel_qgp_vfpriceratio",
    ],
}

settings_space={
    'region': ['USA'],
    'universe': ['TOP3000', "TOP1000"],
    'decay': [5, 10, 15],
    'neutralization': ['SECTOR', 'SUBINDUSTRY'],
    'truncation': [0.01, 0.08],
}
