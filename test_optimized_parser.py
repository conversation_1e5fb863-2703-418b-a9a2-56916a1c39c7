#!/usr/bin/env python3
"""
Test script to verify the optimized template parser performance improvements.

This script tests the new bulk INSERT implementation against a small dataset
to ensure correctness and measure performance improvements.
"""

import time
import logging
from template_parser import TemplateParser

# Configure logging
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(name)s - %(levelname)s - %(message)s'
)

def test_optimized_parser():
    """Test the optimized parser with a small example."""
    
    # Small test template and parameter space
    template = """
data=<data>;
databf=ts_backfill(data, <bf_days>);
signal=<sign>group_neutralize(databf, <group>);
ts_signal=<ts_op>(signal, <op_days>);
<op>(ts_signal)
"""

    parameter_space = {
        "<sign>": ["", "-"],
        "<bf_days>": ["21", "63"],
        "<op_days>": ["1", "21"],
        "<group>": ["sector", "industry"],
        "<op>": ["rank", "zscore"],
        "<ts_op>": ["ts_delta", "ts_zscore"],
        "<data>": [
            "mdl77_2rel5yfwdep",
            "mdl77_2garpanalystmodel_qgp_vfpriceratio",
        ],
    }

    settings_space = {
        'region': ['USA'],
        'universe': ['TOP3000'],
        'decay': [5, 15],
        'neutralization': ['SUBINDUSTRY'],
        'truncation': [0.08],
    }

    # Calculate expected combinations
    param_combinations = 1
    for values in parameter_space.values():
        param_combinations *= len(values)

    settings_combinations = 1
    for values in settings_space.values():
        settings_combinations *= len(values)

    total_expected = param_combinations * settings_combinations

    print("=" * 80)
    print("🧪 TESTING OPTIMIZED TEMPLATE PARSER")
    print("=" * 80)
    print(f"📊 Expected combinations: {total_expected:,}")
    print(f"🔧 Parameter combinations: {param_combinations}")
    print(f"⚙️  Settings combinations: {settings_combinations}")
    print("=" * 80)

    # Test with different batch sizes
    batch_sizes = [10, 50, 100]
    
    for batch_size in batch_sizes:
        print(f"\n🚀 Testing with batch size: {batch_size}")
        print("-" * 40)
        
        # Test in dry run mode first
        print("🧪 DRY RUN MODE:")
        parser_dry = TemplateParser(
            batch_size=batch_size,
            max_workers=1,
            use_threading=False,
            dry_run=True
        )
        
        start_time = time.time()
        template_id, alpha_count = parser_dry.parse_template(
            template, parameter_space, settings_space
        )
        dry_run_time = time.time() - start_time
        
        print(f"   ⏱️  Dry run time: {dry_run_time:.2f} seconds")
        print(f"   🎯 Alphas processed: {alpha_count:,}")
        print(f"   🚀 Rate: {alpha_count/dry_run_time:.1f} alphas/second")
        
        # Test in normal mode (commented out to avoid actual DB writes during testing)
        # Uncomment the following lines to test actual database operations:
        """
        print("💾 NORMAL MODE:")
        parser_normal = TemplateParser(
            batch_size=batch_size,
            max_workers=1,
            use_threading=False,
            dry_run=False
        )
        
        start_time = time.time()
        template_id, alpha_count = parser_normal.parse_template(
            template, parameter_space, settings_space
        )
        normal_time = time.time() - start_time
        
        print(f"   ⏱️  Normal time: {normal_time:.2f} seconds")
        print(f"   🎯 Alphas saved: {alpha_count:,}")
        print(f"   🚀 Rate: {alpha_count/normal_time:.1f} alphas/second")
        print(f"   📊 Template ID: {template_id}")
        """

    print("\n" + "=" * 80)
    print("✅ OPTIMIZATION TEST COMPLETED!")
    print("=" * 80)
    print("🔧 Key improvements implemented:")
    print("   • Bulk INSERT using PostgreSQL UNNEST function")
    print("   • Reduced database round-trips from N to 2 queries per batch")
    print("   • Added unique constraint on alpha_reference table")
    print("   • Optimized alpha_reference insertion with single query")
    print("=" * 80)

if __name__ == "__main__":
    test_optimized_parser()
